import * as THREE from 'three';
// 导入 Three.js 附加组件，例如轨道控制器或 GLTF 加载器
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
// === 基本场景、相机与渲染器（Renderer）初始化 ===
const container = document.getElementById('app')!!;
const scene = new THREE.Scene();
scene.background = new THREE.Color(0xaaaaaa);

const camera = new THREE.PerspectiveCamera(
    60,                                   // 视角（视野角度）
    window.innerWidth / window.innerHeight, // 长宽比
    0.1,                                  // 近裁剪面
    1000                                  // 远裁剪面
);
camera.position.set(0, 10, 20); // 将相机放在稍高一些并远离场景

const renderer = new THREE.WebGLRenderer({antialias: true});
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.shadowMap.enabled = true; // 启用阴影
container.appendChild(renderer.domElement);

// 自适应窗口大小变化
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});

// === 添加灯光 ===
// 环境光，让整个场景不会过暗
const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
scene.add(ambientLight);

// 平行光 (类似太阳光)，用于产生阴影
const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
dirLight.position.set(10, 20, 10);
dirLight.castShadow = true;
dirLight.shadow.camera.left = -20;
dirLight.shadow.camera.right = 20;
dirLight.shadow.camera.top = 20;
dirLight.shadow.camera.bottom = -20;
dirLight.shadow.mapSize.set(2048, 2048);
scene.add(dirLight);

// 点光源，模拟桌面台灯或者实验台上的光源
const pointLight = new THREE.PointLight(0xffffff, 0.6, 50);
pointLight.position.set(-5, 8, -5);
pointLight.castShadow = true;
scene.add(pointLight);

// === 地面与网格辅助线 ===
// 加一个大平面做“实验室地板”
const floorGeo = new THREE.PlaneGeometry(50, 50);
const floorMat = new THREE.MeshStandardMaterial({color: 0xeeeeee});
const floor = new THREE.Mesh(floorGeo, floorMat);
floor.rotation.x = -Math.PI / 2;
floor.receiveShadow = true;
scene.add(floor);

// 可选：添加网格辅助线，方便观察比例
const gridHelper = new THREE.GridHelper(50, 50, 0x444444, 0x888888);
scene.add(gridHelper);

// === OrbitControls，用于鼠标交互 ===
const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;
controls.minDistance = 5;
controls.maxDistance = 100;
controls.maxPolarAngle = Math.PI / 2.2; // 限制仰角，防止看见地板以下

// === 实验台（Lab Bench）模型 ===
function createLabBench(positionX: number) {
    const benchGroup = new THREE.Group();

    // 台面
    const topGeo = new THREE.BoxGeometry(6, 0.3, 2);
    const topMat = new THREE.MeshStandardMaterial({color: 0x555555});
    const topMesh = new THREE.Mesh(topGeo, topMat);
    topMesh.position.y = 1.1;
    topMesh.castShadow = true;
    topMesh.receiveShadow = true;
    benchGroup.add(topMesh);

    // 四条桌腿
    const legGeo = new THREE.BoxGeometry(0.2, 2, 0.2);
    const legMat = new THREE.MeshStandardMaterial({color: 0x333333});
    const legPositions = [
        [-2.9, 0, -0.9],
        [2.9, 0, -0.9],
        [-2.9, 0, 0.9],
        [2.9, 0, 0.9]
    ];
    legPositions.forEach(pos => {
        const leg = new THREE.Mesh(legGeo, legMat);
        leg.position.set(pos[0], 0.0, pos[2]);
        leg.castShadow = true;
        leg.receiveShadow = true;
        benchGroup.add(leg);
    });

    benchGroup.position.x = positionX;
    benchGroup.position.z = 0;
    scene.add(benchGroup);

    return benchGroup;
}

// 创建左侧和右侧各一张实验台
createLabBench(-8);
createLabBench(8);
// === 实验器材：简易烧杯、试管架等 ===
// 烧杯模型
function createBeaker(position: THREE.Vector3Like) {
    const beakerGeo = new THREE.CylinderGeometry(0.3, 0.3, 0.6, 32, 1, true);
    const beakerMat = new THREE.MeshStandardMaterial({
        color: 0x88ccee,
        transparent: true,
        opacity: 0.6,
        side: THREE.DoubleSide
    });
    const beaker = new THREE.Mesh(beakerGeo, beakerMat);
    beaker.position.copy(position);
    beaker.position.y = 1.4;
    beaker.castShadow = true;
    beaker.receiveShadow = true;
    scene.add(beaker);
    return beaker;
}

// 试管架（简易版：底座 + 圆柱孔洞示意）
function createTestTubeRack(position: THREE.Vector3Like) {
    const rackGroup = new THREE.Group();

    // 底座
    const baseGeo = new THREE.BoxGeometry(1.5, 0.2, 0.5);
    const baseMat = new THREE.MeshStandardMaterial({color: 0x776655});
    const base = new THREE.Mesh(baseGeo, baseMat);
    base.position.y = 1.2;
    rackGroup.add(base);

    // 几个垂直支柱，模拟插试管的孔
    const holeGeo = new THREE.CylinderGeometry(0.08, 0.08, 0.4, 16);
    const holeMat = new THREE.MeshStandardMaterial({color: 0x333333});
    for (let i = -2; i <= 2; i++) {
        const hole = new THREE.Mesh(holeGeo, holeMat);
        hole.position.set(i * 0.3, 1.4, 0);
        rackGroup.add(hole);
    }

    rackGroup.position.copy(position);
    scene.add(rackGroup);
    return rackGroup;
}

// 简易试管模型
function createTestTube(position: THREE.Vector3Like) {
    const tubeGeo = new THREE.CylinderGeometry(0.1, 0.1, 1.2, 32, 1, true);
    const tubeMat = new THREE.MeshStandardMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0.5,
        side: THREE.DoubleSide
    });
    const tube = new THREE.Mesh(tubeGeo, tubeMat);
    tube.position.copy(position);
    tube.position.y = 1.8;
    tube.castShadow = true;
    tube.receiveShadow = true;
    scene.add(tube);
    return tube;
}

// 在左侧实验台上放置一个烧杯和一个试管架，并在试管架中插几根试管
createBeaker(new THREE.Vector3(-8, 0, -0.5));
createTestTubeRack(new THREE.Vector3(-8, 0, 0.5));
for (let i = -2; i <= 2; i++) {
    createTestTube(new THREE.Vector3(-8 + i * 0.3, 0, 0.5));
}

// 在右侧实验台上放置两个烧杯
createBeaker(new THREE.Vector3(8, 0, -0.5));
createBeaker(new THREE.Vector3(8, 0, 0.5));

// === 实验室背景可选：墙壁与窗户 ===
function createWall(width: number | undefined, height: number | undefined, depth: number | undefined, position: THREE.Vector3Like, rotationY = 0) {
    const wallGeo = new THREE.BoxGeometry(width, height, depth);
    const wallMat = new THREE.MeshStandardMaterial({color: 0xdddddd});
    const wall = new THREE.Mesh(wallGeo, wallMat);
    wall.position.copy(position);
    wall.rotation.y = rotationY;
    wall.receiveShadow = true;
    scene.add(wall);
    return wall;
}

// 后墙
createWall(50, 10, 0.5, new THREE.Vector3(0, 5, -25));
// 左侧墙
createWall(50, 10, 0.5, new THREE.Vector3(-25, 5, 0), Math.PI / 2);
// 右侧墙
createWall(50, 10, 0.5, new THREE.Vector3(25, 5, 0), -Math.PI / 2);
// 前面可以留空，让用户从前面进入

// 在后墙上添加一个简单的窗户框和玻璃
function createWindow(position: THREE.Vector3Like) {
    // 窗框
    const frameGeo = new THREE.BoxGeometry(5, 3, 0.1);
    const frameMat = new THREE.MeshStandardMaterial({color: 0x444444});
    const frame = new THREE.Mesh(frameGeo, frameMat);
    frame.position.copy(position);
    frame.receiveShadow = true;
    scene.add(frame);

    // 玻璃
    const glassGeo = new THREE.PlaneGeometry(4.5, 2.5);
    const glassMat = new THREE.MeshStandardMaterial({
        color: 0x77bbff,
        transparent: true,
        opacity: 0.4,
        side: THREE.DoubleSide
    });
    const glass = new THREE.Mesh(glassGeo, glassMat);
    glass.position.copy(position);
    glass.position.z += 0.06;
    scene.add(glass);
}

createWindow(new THREE.Vector3(0, 5, -24.75));

// === 渲染循环 ===
function animate() {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
}

animate();