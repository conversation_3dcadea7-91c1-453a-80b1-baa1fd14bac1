# 虚拟光学实验室

一个基于 Three.js 的交互式光学实验室，支持自定义透镜和光源的光学实验仿真。

## 功能特性

### 透镜系统
- **凸透镜**：中间厚、边缘薄，具有会聚光线的特性
- **凹透镜**：中间薄、边缘厚，具有发散光线的特性
- **可调参数**：
  - 焦距：-50 到 50 范围内调节
  - 半径：5 到 20 范围内调节
  - 厚度：1 到 8 范围内调节
  - 位置：X、Y、Z 三个方向可调

### 光源系统
- **点光源**：从一个点向四周发射光线
- **平行光**：平行光束，模拟远距离光源（如太阳光）
- **可调参数**：
  - 强度：0 到 3 范围内调节
  - 位置：X、Y、Z 三个方向可调
  - 颜色：支持自定义颜色

### 光线可视化
- **实时光线追踪**：显示光源到透镜、透镜到焦点的光路
- **光线颜色区分**：
  - 黄色：光源到透镜的入射光线
  - 橙色：透镜到焦点的折射光线
- **动态更新**：参数改变时实时更新光路

## 使用方法

### 启动项目
```bash
npm install
npm run dev
```

### 控制面板操作

#### 透镜控制
1. **添加透镜**：
   - 点击"添加凸透镜"或"添加凹透镜"
   - 新透镜会出现在场景中心

2. **调节透镜参数**：
   - 在"透镜参数"面板中选择要调节的透镜
   - 拖动滑块调节焦距、半径、厚度
   - 调节 X、Y、Z 位置移动透镜

3. **删除透镜**：
   - 在对应透镜的参数面板中点击"删除透镜"

#### 光源控制
1. **添加光源**：
   - 点击"添加点光源"或"添加平行光"
   - 新光源会出现在默认位置

2. **调节光源参数**：
   - 在"光源参数"面板中选择要调节的光源
   - 调节强度和位置参数
   - 实时查看光线变化

3. **删除光源**：
   - 在对应光源的参数面板中点击"删除光源"

#### 光线控制
- **显示/隐藏光线**：切换"显示光线"开关
- **更新光线**：点击"更新光线"手动刷新光路

### 场景操作
- **旋转视角**：鼠标左键拖拽
- **缩放**：鼠标滚轮
- **平移**：鼠标右键拖拽

## 光学原理

### 凸透镜
- 焦距为正值
- 平行光经过凸透镜后会聚于焦点
- 中心厚度大于边缘厚度

### 凹透镜
- 焦距为负值（虚焦点）
- 平行光经过凹透镜后发散，反向延长线交于虚焦点
- 中心厚度小于边缘厚度

### 光线追踪
- 简化的光线追踪模型
- 显示主要光路：入射光线和折射光线
- 实时计算焦点位置

## 技术栈
- **Three.js**：3D 图形渲染
- **TypeScript**：类型安全的 JavaScript
- **lil-gui**：参数控制界面
- **Vite**：快速开发构建工具

## 项目结构
```
src/
├── lens.ts          # 主要实验室代码
├── main.ts          # 基础 Three.js 示例
├── test.ts          # 实验室场景示例
└── style.css        # 样式文件
```

## 扩展功能建议
- 添加更多透镜类型（双凸、双凹等）
- 实现更精确的光线追踪算法
- 添加光谱分析功能
- 支持透镜组合实验
- 添加测量工具（距离、角度等）
