# 虚拟光学实验室演示指南

## 快速开始

1. **启动项目**
   ```bash
   npm run dev
   ```
   
2. **打开浏览器**
   访问 http://localhost:5174

3. **查看初始场景**
   - 场景中已经有一个凸透镜（左侧，蓝色）
   - 一个凹透镜（右侧，粉色）
   - 一个点光源（左上方）
   - 黄色和橙色的光线显示光路

## 实验演示

### 实验一：凸透镜聚焦实验

1. **观察初始状态**
   - 左侧凸透镜的焦距为 20
   - 光线从点光源发出，经过透镜后聚焦

2. **调节焦距**
   - 在右侧控制面板找到"透镜参数" → "凸透镜 1"
   - 拖动"焦距"滑块，观察焦点位置变化
   - 焦距越小，焦点越近

3. **调节透镜位置**
   - 调节 X、Y、Z 位置参数
   - 观察光路如何随透镜移动而变化

### 实验二：凹透镜发散实验

1. **观察凹透镜**
   - 右侧凹透镜的焦距为 -15（负值表示虚焦点）
   - 光线经过凹透镜后发散

2. **调节凹透镜参数**
   - 在"凹透镜 1"面板中调节参数
   - 观察虚焦点位置变化

### 实验三：多光源实验

1. **添加新光源**
   - 点击"光源控制" → "添加平行光"
   - 观察平行光与点光源的不同效果

2. **调节光源参数**
   - 在"光源参数"中调节新光源的位置和强度
   - 观察不同光源产生的光路

### 实验四：透镜组合实验

1. **添加新透镜**
   - 点击"透镜控制" → "添加凸透镜"
   - 新透镜出现在场景中心

2. **创建透镜组**
   - 调节新透镜的位置，使其与现有透镜形成组合
   - 观察光线经过多个透镜的路径

3. **调节透镜间距**
   - 改变透镜之间的距离
   - 观察对最终焦点的影响

## 操作技巧

### 视角控制
- **旋转**：鼠标左键拖拽
- **缩放**：鼠标滚轮
- **平移**：鼠标右键拖拽

### 参数调节
- **精确调节**：直接在输入框中输入数值
- **快速调节**：拖拽滑块
- **实时预览**：参数改变时光路立即更新

### 光线显示
- **隐藏光线**：取消勾选"显示光线"
- **更新光线**：点击"更新光线"按钮
- **光线颜色**：
  - 黄色：入射光线
  - 橙色：折射光线

## 实验建议

### 基础实验
1. **焦距测量**：调节透镜位置，找到最佳聚焦点
2. **放大倍数**：观察不同焦距透镜的放大效果
3. **光路可逆性**：改变光源位置验证光路可逆原理

### 进阶实验
1. **望远镜原理**：用两个凸透镜组成简单望远镜
2. **显微镜原理**：短焦距物镜 + 长焦距目镜
3. **光学系统设计**：设计特定功能的透镜组合

### 创新实验
1. **异常光路**：创建特殊的光源和透镜配置
2. **光学仿真**：模拟真实光学仪器
3. **教学演示**：为光学教学设计演示实验

## 故障排除

### 常见问题
1. **光线不显示**：检查"显示光线"是否开启
2. **透镜不可见**：调节相机位置或透镜位置
3. **参数无效果**：点击"更新光线"刷新

### 性能优化
1. **减少透镜数量**：过多透镜可能影响性能
2. **简化光线**：关闭光线显示可提高帧率
3. **重置场景**：清除所有元素重新开始

## 扩展学习

### 光学原理
- 费马原理
- 斯涅尔定律
- 高斯光学
- 几何光学

### 编程学习
- Three.js 3D 编程
- TypeScript 开发
- 光线追踪算法
- 物理仿真
