<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>虚拟仿真实验室</title>
    <style>
    body { margin: 0; overflow: hidden; }
    #app { width: 100%; height: 100vh; }
  </style>
  </head>
  <body>
    <!-- Three.js 渲染器会自动添加到这里，或添加到指定 div 中 -->
    <div id="app"></div>
    <script type="module" src="/src/lens.ts"></script>
  </body>
</html>