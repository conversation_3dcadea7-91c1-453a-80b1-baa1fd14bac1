import * as THREE from 'three';
// 导入 Three.js 附加组件，例如轨道控制器或 GLTF 加载器
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

const container = document.getElementById('app')!!;

// =============================================
// 1. 场景、相机、渲染器 初始化
// =============================================
const scene = new THREE.Scene();
scene.background = new THREE.Color(0x101020);

const camera = new THREE.PerspectiveCamera(
    45,
    window.innerWidth / window.innerHeight,
    0.1,
    100
);
camera.position.set(0, -6, 4);
camera.up.set(0, 0, 1);
camera.lookAt(0, 0, 0);

const renderer = new THREE.WebGLRenderer({antialias: true});
renderer.setSize(window.innerWidth, window.innerHeight);
container.appendChild(renderer.domElement);

window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});

const controls = new OrbitControls(camera, renderer.domElement);
controls.target.set(0, 0, 0);
controls.update();

// =============================================
// 2. 环形磁体（Torus）
// =============================================
const magnetOuterRadius = 1.5;
const magnetTubeRadius = 0.3;
const magnetGeometry = new THREE.TorusGeometry(
    magnetOuterRadius,
    magnetTubeRadius,
    32,
    100
);
const magnetMaterial = new THREE.MeshStandardMaterial({
    color: 0x222255,
    metalness: 0.8,
    roughness: 0.2,
});
const ringMagnet = new THREE.Mesh(magnetGeometry, magnetMaterial);
ringMagnet.rotation.x = Math.PI / 2;
ringMagnet.position.set(0, 0, 0);
scene.add(ringMagnet);

const keyLight = new THREE.DirectionalLight(0xffffff, 0.8);
keyLight.position.set(5, -5, 5);
scene.add(keyLight);

const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
scene.add(ambientLight);

// =============================================
// 3. 石墨烯薄层（CircleGeometry + ShaderMaterial），去掉了 shape.vertices 操作
// =============================================
const Rc = 1.0;
const planeRadius = 1.2;
const planeSegments = 128;

// 直接创建 CircleGeometry (其实是 BufferGeometry)
const shape = new THREE.CircleGeometry(planeRadius, planeSegments);

// 下面不再尝试 shape.vertices.shift()

const planeMaterial = new THREE.ShaderMaterial({
    uniforms: {
        u_Rc: {value: Rc},
        u_planeRadius: {value: planeRadius},
        u_colorCenter: {value: new THREE.Color(0xff5555)},
        u_colorEdge: {value: new THREE.Color(0x5555ff)},
        u_heightScale: {value: 0.2},
    },
    vertexShader: `
        uniform float u_Rc;
        uniform float u_heightScale;
        varying float v_r;
        void main() {
          vec3 pos = position;
          float r = length(pos.xy);
          v_r = r;
          float z_off = (r * r) / (u_Rc * u_Rc) * u_heightScale;
          pos.z += z_off;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        }
      `,
    fragmentShader: `
        uniform float u_planeRadius;
        uniform vec3 u_colorCenter;
        uniform vec3 u_colorEdge;
        varying float v_r;
        void main() {
          float t = clamp(v_r / u_planeRadius, 0.0, 1.0);
          vec3 color = mix(u_colorCenter, u_colorEdge, t);
          gl_FragColor = vec4(color, 0.8);
        }
      `,
    side: THREE.DoubleSide,
    transparent: true,
});

const omlPlane = new THREE.Mesh(shape, planeMaterial);
omlPlane.rotation.x = Math.PI / 2;
omlPlane.position.set(0, 0, 0);
scene.add(omlPlane);

// =============================================
// 4. 高斯光束示意（Line）
// =============================================
const beamGroup = new THREE.Group();
scene.add(beamGroup);

const beamColor = 0xffff66;
const beamSegments = 50;
const beamLength = 6.0;
const beamHalfWidth = 0.6;
const sampleRays = 11;

for (let i = -(sampleRays - 1) / 2; i <= (sampleRays - 1) / 2; i++) {
    const offsetY = (i / ((sampleRays - 1) / 2)) * beamHalfWidth;
    const points = [];

    for (let j = 0; j <= beamSegments / 2; j++) {
        const t = j / (beamSegments / 2);
        const z = -beamLength * (1.0 - t);
        const y = offsetY * (1.0 - t);
        const x = -beamLength * (1.0 - t);
        points.push(new THREE.Vector3(x, y, z));
    }

    for (let j = 0; j <= beamSegments / 2; j++) {
        const t = j / (beamSegments / 2);
        const z = beamLength * t;
        const y = offsetY * (1.0 - t);
        const x = beamLength * t;
        points.push(new THREE.Vector3(x, y, z));
    }

    const beamGeometry = new THREE.BufferGeometry().setFromPoints(points);
    const beamMaterial = new THREE.LineBasicMaterial({
        color: beamColor,
        linewidth: 2,
        transparent: true,
        opacity: 0.6,
    });
    const beamLine = new THREE.Line(beamGeometry, beamMaterial);
    beamGroup.add(beamLine);
}

beamGroup.rotation.z = Math.PI / 2;
beamGroup.rotation.x = -Math.PI / 2;

// =============================================
// 5. 坐标轴辅助线
// =============================================
const axesHelper = new THREE.AxesHelper(2.5);
scene.add(axesHelper);

// =============================================
// 6. 渲染循环
// =============================================
function animate() {
    requestAnimationFrame(animate);
    ringMagnet.rotation.z += 0.002;
    omlPlane.rotation.z += 0.0005;
    controls.update();
    renderer.render(scene, camera);
}

animate();
