import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { GUI } from 'lil-gui';

const container = document.getElementById('app')!!;

const scene = new THREE.Scene();
scene.background = new THREE.Color(0x222222);

const camera = new THREE.PerspectiveCamera(
    45,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
);
camera.position.set(0, 20, 50);

const renderer = new THREE.WebGLRenderer({antialias: true});
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.shadowMap.enabled = true;
renderer.shadowMap.type = THREE.PCFSoftShadowMap;
container.appendChild(renderer.domElement);

// 添加轨道控制
const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;

// 透镜类定义
class Lens {
    mesh: THREE.Mesh;
    type: 'convex' | 'concave';
    focalLength: number;
    radius: number;
    thickness: number;
    position: THREE.Vector3;

    constructor(type: 'convex' | 'concave', focalLength: number, radius: number = 10, thickness: number = 4) {
        this.type = type;
        this.focalLength = focalLength;
        this.radius = radius;
        this.thickness = thickness;
        this.position = new THREE.Vector3(0, 0, 0);

        this.mesh = this.createLensMesh();
        this.updatePosition();
    }

    createLensMesh(): THREE.Mesh {
        const points = [];
        const segments = 64;

        // 根据透镜类型创建不同的轮廓
        if (this.type === 'convex') {
            // 凸透镜：中间厚，边缘薄
            const curvature = Math.abs(this.focalLength) / 10; // 焦距影响曲率
            for (let x = 0; x <= this.radius; x += 0.5) {
                const y = this.thickness / 2 - (x * x) / curvature;
                points.push(new THREE.Vector2(x, Math.max(y, 0.2))); // 最小厚度0.2
            }
        } else {
            // 凹透镜：中间薄，边缘厚
            const curvature = Math.abs(this.focalLength) / 10;
            for (let x = 0; x <= this.radius; x += 0.5) {
                const y = 0.2 + (x * x) / curvature; // 中心最薄0.2
                points.push(new THREE.Vector2(x, Math.min(y, this.thickness / 2)));
            }
        }
        points.push(new THREE.Vector2(this.radius, 0));

        const latheGeo = new THREE.LatheGeometry(points, segments);

        const material = new THREE.MeshPhysicalMaterial({
            color: this.type === 'convex' ? 0xccccff : 0xffcccc,
            metalness: 0,
            roughness: 0,
            transmission: 0.9,
            transparent: true,
            opacity: 0.8,
            ior: 1.5,
            clearcoat: 1.0,
            clearcoatRoughness: 0.1
        });

        const mesh = new THREE.Mesh(latheGeo, material);
        mesh.rotateX(Math.PI / 2);
        mesh.castShadow = true;
        mesh.receiveShadow = true;

        return mesh;
    }

    updateLens() {
        // 移除旧的mesh
        scene.remove(this.mesh);

        // 创建新的mesh
        this.mesh = this.createLensMesh();
        this.updatePosition();

        // 添加到场景
        scene.add(this.mesh);
    }

    updatePosition() {
        this.mesh.position.copy(this.position);
    }

    setPosition(x: number, y: number, z: number) {
        this.position.set(x, y, z);
        this.updatePosition();
    }

    setFocalLength(focalLength: number) {
        this.focalLength = focalLength;
        this.updateLens();
    }

    setRadius(radius: number) {
        this.radius = radius;
        this.updateLens();
    }

    setThickness(thickness: number) {
        this.thickness = thickness;
        this.updateLens();
    }
}

// 光源类定义
class LightSource {
    light: THREE.DirectionalLight | THREE.PointLight;
    helper: THREE.DirectionalLightHelper | THREE.PointLightHelper;
    type: 'point' | 'directional';
    intensity: number;
    color: number;
    position: THREE.Vector3;
    direction: THREE.Vector3;

    constructor(type: 'point' | 'directional', intensity: number = 1, color: number = 0xffffff) {
        this.type = type;
        this.intensity = intensity;
        this.color = color;
        this.position = new THREE.Vector3(15, 10, 15);
        this.direction = new THREE.Vector3(-1, -1, -1).normalize();

        if (type === 'point') {
            this.light = new THREE.PointLight(color, intensity, 100);
            this.helper = new THREE.PointLightHelper(this.light as THREE.PointLight, 2);
        } else {
            this.light = new THREE.DirectionalLight(color, intensity);
            this.helper = new THREE.DirectionalLightHelper(this.light as THREE.DirectionalLight, 5);
        }

        this.setupShadows();
        this.updatePosition();
    }

    setupShadows() {
        this.light.castShadow = true;
        this.light.shadow.mapSize.width = 1024;
        this.light.shadow.mapSize.height = 1024;
        this.light.shadow.camera.near = 0.5;
        this.light.shadow.camera.far = 500;

        if (this.type === 'directional') {
            const d = 50;
            this.light.shadow.camera.left = -d;
            this.light.shadow.camera.right = d;
            this.light.shadow.camera.top = d;
            this.light.shadow.camera.bottom = -d;
        }
    }

    updatePosition() {
        this.light.position.copy(this.position);
        if (this.type === 'directional') {
            const target = new THREE.Vector3().addVectors(this.position, this.direction.clone().multiplyScalar(10));
            (this.light as THREE.DirectionalLight).target.position.copy(target);
        }
    }

    setPosition(x: number, y: number, z: number) {
        this.position.set(x, y, z);
        this.updatePosition();
    }

    setIntensity(intensity: number) {
        this.intensity = intensity;
        this.light.intensity = intensity;
    }

    setColor(color: number) {
        this.color = color;
        this.light.color.setHex(color);
    }

    addToScene(scene: THREE.Scene) {
        scene.add(this.light);
        scene.add(this.helper);
        if (this.type === 'directional') {
            scene.add((this.light as THREE.DirectionalLight).target);
        }
    }

    removeFromScene(scene: THREE.Scene) {
        scene.remove(this.light);
        scene.remove(this.helper);
        if (this.type === 'directional') {
            scene.remove((this.light as THREE.DirectionalLight).target);
        }
    }
}

// 光线可视化类
class LightRay {
    geometry: THREE.BufferGeometry;
    material: THREE.LineBasicMaterial;
    line: THREE.Line;

    constructor(start: THREE.Vector3, end: THREE.Vector3, color: number = 0xffff00) {
        const points = [start, end];
        this.geometry = new THREE.BufferGeometry().setFromPoints(points);
        this.material = new THREE.LineBasicMaterial({ color: color, transparent: true, opacity: 0.8 });
        this.line = new THREE.Line(this.geometry, this.material);
    }

    updateRay(start: THREE.Vector3, end: THREE.Vector3) {
        const points = [start, end];
        this.geometry.setFromPoints(points);
    }

    addToScene(scene: THREE.Scene) {
        scene.add(this.line);
    }

    removeFromScene(scene: THREE.Scene) {
        scene.remove(this.line);
    }
}

// 初始化场景
const ambientLight = new THREE.AmbientLight(0xffffff, 0.2);
scene.add(ambientLight);

// 创建地面
const planeGeometry = new THREE.PlaneGeometry(100, 100);
const planeMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
const plane = new THREE.Mesh(planeGeometry, planeMaterial);
plane.rotation.x = -Math.PI / 2;
plane.position.y = -15;
plane.receiveShadow = true;
scene.add(plane);

// 创建坐标轴辅助器
const axesHelper = new THREE.AxesHelper(20);
scene.add(axesHelper);

// 管理器
let lenses: Lens[] = [];
let lightSources: LightSource[] = [];
let lightRays: LightRay[] = [];

// 初始化默认透镜
function initializeDefaultLenses() {
    const convexLens = new Lens('convex', 20, 10, 4);
    convexLens.setPosition(-20, 0, 0);
    scene.add(convexLens.mesh);
    lenses.push(convexLens);

    const concaveLens = new Lens('concave', -15, 8, 3);
    concaveLens.setPosition(20, 0, 0);
    scene.add(concaveLens.mesh);
    lenses.push(concaveLens);
}

// 添加光源函数
function addLightSource(type: 'point' | 'directional') {
    const lightSource = new LightSource(type);
    lightSource.addToScene(scene);
    lightSources.push(lightSource);
    return lightSource;
}

// 移除光源函数
function removeLightSource(index: number) {
    if (index >= 0 && index < lightSources.length) {
        lightSources[index].removeFromScene(scene);
        lightSources.splice(index, 1);
    }
}

// 清除所有光源
function clearAllLights() {
    lightSources.forEach(lightSource => {
        lightSource.removeFromScene(scene);
    });
    lightSources = [];
}

// 添加透镜函数
function addLens(type: 'convex' | 'concave') {
    const lens = new Lens(type, type === 'convex' ? 20 : -15);
    lens.setPosition(0, 0, 0);
    scene.add(lens.mesh);
    lenses.push(lens);
    return lens;
}

// 移除透镜函数
function removeLens(index: number) {
    if (index >= 0 && index < lenses.length) {
        scene.remove(lenses[index].mesh);
        lenses.splice(index, 1);
    }
}

// 清除所有透镜
function clearAllLenses() {
    lenses.forEach(lens => {
        scene.remove(lens.mesh);
    });
    lenses = [];
}

// 光线追踪计算（简化版）
function calculateLightRays() {
    // 清除现有光线
    lightRays.forEach(ray => ray.removeFromScene(scene));
    lightRays = [];

    // 为每个光源和透镜组合计算光线
    lightSources.forEach(lightSource => {
        lenses.forEach(lens => {
            // 简化的光线追踪：从光源到透镜，再从透镜到焦点
            const lightPos = lightSource.position.clone();
            const lensPos = lens.position.clone();

            // 光源到透镜的光线
            const ray1 = new LightRay(lightPos, lensPos, 0xffff00);
            ray1.addToScene(scene);
            lightRays.push(ray1);

            // 透镜到焦点的光线（简化计算）
            let focalPoint: THREE.Vector3;
            if (lens.type === 'convex') {
                focalPoint = new THREE.Vector3(lensPos.x + lens.focalLength, lensPos.y, lensPos.z);
            } else {
                // 凹透镜的虚焦点
                focalPoint = new THREE.Vector3(lensPos.x - Math.abs(lens.focalLength), lensPos.y, lensPos.z);
            }

            const ray2 = new LightRay(lensPos, focalPoint, 0xff6600);
            ray2.addToScene(scene);
            lightRays.push(ray2);
        });
    });
}

// 全局GUI变量
let gui: GUI;
let lensParamsFolder: any = null;
let lightParamsFolder: any = null;

// 动态更新控制器函数
function updateLensControllers() {
    // 移除旧的透镜控制器
    if (lensParamsFolder) {
        gui.removeFolder(lensParamsFolder);
        lensParamsFolder = null;
    }

    // 添加新的透镜控制器
    if (lenses.length > 0) {
        lensParamsFolder = gui.addFolder('透镜参数');
        lenses.forEach((lens, index) => {
            const lensSubFolder = lensParamsFolder.addFolder(`${lens.type === 'convex' ? '凸' : '凹'}透镜 ${index + 1}`);

            lensSubFolder.add(lens, 'focalLength', -50, 50, 1).name('焦距').onChange(() => {
                lens.setFocalLength(lens.focalLength);
                calculateLightRays();
            });

            lensSubFolder.add(lens, 'radius', 5, 20, 0.5).name('半径').onChange(() => {
                lens.setRadius(lens.radius);
                calculateLightRays();
            });

            lensSubFolder.add(lens, 'thickness', 1, 8, 0.1).name('厚度').onChange(() => {
                lens.setThickness(lens.thickness);
                calculateLightRays();
            });

            lensSubFolder.add(lens.position, 'x', -50, 50, 1).name('X位置').onChange(() => {
                lens.updatePosition();
                calculateLightRays();
            });

            lensSubFolder.add(lens.position, 'y', -20, 20, 1).name('Y位置').onChange(() => {
                lens.updatePosition();
                calculateLightRays();
            });

            lensSubFolder.add(lens.position, 'z', -50, 50, 1).name('Z位置').onChange(() => {
                lens.updatePosition();
                calculateLightRays();
            });

            lensSubFolder.add({ remove: () => {
                removeLens(index);
                updateLensControllers();
                calculateLightRays();
            }}, 'remove').name('删除透镜');
        });
    }
}

function updateLightControllers() {
    // 移除旧的光源控制器
    if (lightParamsFolder) {
        gui.removeFolder(lightParamsFolder);
        lightParamsFolder = null;
    }

    // 添加新的光源控制器
    if (lightSources.length > 0) {
        lightParamsFolder = gui.addFolder('光源参数');
        lightSources.forEach((lightSource, index) => {
            const lightSubFolder = lightParamsFolder.addFolder(`${lightSource.type === 'point' ? '点' : '平行'}光源 ${index + 1}`);

            lightSubFolder.add(lightSource, 'intensity', 0, 3, 0.1).name('强度').onChange(() => {
                lightSource.setIntensity(lightSource.intensity);
            });

            lightSubFolder.add(lightSource.position, 'x', -50, 50, 1).name('X位置').onChange(() => {
                lightSource.updatePosition();
                calculateLightRays();
            });

            lightSubFolder.add(lightSource.position, 'y', -20, 50, 1).name('Y位置').onChange(() => {
                lightSource.updatePosition();
                calculateLightRays();
            });

            lightSubFolder.add(lightSource.position, 'z', -50, 50, 1).name('Z位置').onChange(() => {
                lightSource.updatePosition();
                calculateLightRays();
            });

            lightSubFolder.add({ remove: () => {
                removeLightSource(index);
                updateLightControllers();
                calculateLightRays();
            }}, 'remove').name('删除光源');
        });
    }
}

// 创建GUI控制界面
function createGUI() {
    gui = new GUI();
    gui.title('光学实验室控制面板');

    // 透镜控制
    const lensFolder = gui.addFolder('透镜控制');

    const lensControls = {
        addConvexLens: () => {
            console.log('添加凸透镜被点击');
            const lens = addLens('convex');
            updateLensControllers();
            calculateLightRays();
        },
        addConcaveLens: () => {
            console.log('添加凹透镜被点击');
            const lens = addLens('concave');
            updateLensControllers();
            calculateLightRays();
        },
        clearAllLenses: () => {
            console.log('清除所有透镜被点击');
            clearAllLenses();
            updateLensControllers();
            calculateLightRays();
        }
    };

    lensFolder.add(lensControls, 'addConvexLens').name('添加凸透镜');
    lensFolder.add(lensControls, 'addConcaveLens').name('添加凹透镜');
    lensFolder.add(lensControls, 'clearAllLenses').name('清除所有透镜');

    // 光源控制
    const lightFolder = gui.addFolder('光源控制');

    const lightControls = {
        addPointLight: () => {
            console.log('添加点光源被点击');
            const light = addLightSource('point');
            updateLightControllers();
            calculateLightRays();
        },
        addDirectionalLight: () => {
            console.log('添加平行光被点击');
            const light = addLightSource('directional');
            updateLightControllers();
            calculateLightRays();
        },
        clearAllLights: () => {
            console.log('清除所有光源被点击');
            clearAllLights();
            updateLightControllers();
            calculateLightRays();
        }
    };

    lightFolder.add(lightControls, 'addPointLight').name('添加点光源');
    lightFolder.add(lightControls, 'addDirectionalLight').name('添加平行光');
    lightFolder.add(lightControls, 'clearAllLights').name('清除所有光源');

    // 光线显示控制
    const rayFolder = gui.addFolder('光线控制');
    const rayControls = {
        showRays: true,
        updateRays: () => calculateLightRays()
    };

    rayFolder.add(rayControls, 'showRays').name('显示光线').onChange((value: boolean) => {
        if (!value) {
            lightRays.forEach(ray => ray.removeFromScene(scene));
        } else {
            calculateLightRays();
        }
    });
    rayFolder.add(rayControls, 'updateRays').name('更新光线');

    // 存储GUI文件夹引用
    let lensParamsFolder: any = null;
    let lightParamsFolder: any = null;

    // 动态更新控制器
    function updateLensControllers() {
        // 移除旧的透镜控制器
        if (lensParamsFolder) {
            gui.removeFolder(lensParamsFolder);
            lensParamsFolder = null;
        }

        // 添加新的透镜控制器
        if (lenses.length > 0) {
            lensParamsFolder = gui.addFolder('透镜参数');
            lenses.forEach((lens, index) => {
                const lensSubFolder = lensParamsFolder.addFolder(`${lens.type === 'convex' ? '凸' : '凹'}透镜 ${index + 1}`);

                lensSubFolder.add(lens, 'focalLength', -50, 50, 1).name('焦距').onChange(() => {
                    lens.setFocalLength(lens.focalLength);
                    calculateLightRays();
                });

                lensSubFolder.add(lens, 'radius', 5, 20, 0.5).name('半径').onChange(() => {
                    lens.setRadius(lens.radius);
                    calculateLightRays();
                });

                lensSubFolder.add(lens, 'thickness', 1, 8, 0.1).name('厚度').onChange(() => {
                    lens.setThickness(lens.thickness);
                    calculateLightRays();
                });

                lensSubFolder.add(lens.position, 'x', -50, 50, 1).name('X位置').onChange(() => {
                    lens.updatePosition();
                    calculateLightRays();
                });

                lensSubFolder.add(lens.position, 'y', -20, 20, 1).name('Y位置').onChange(() => {
                    lens.updatePosition();
                    calculateLightRays();
                });

                lensSubFolder.add(lens.position, 'z', -50, 50, 1).name('Z位置').onChange(() => {
                    lens.updatePosition();
                    calculateLightRays();
                });

                lensSubFolder.add({ remove: () => {
                    removeLens(index);
                    updateLensControllers();
                    calculateLightRays();
                }}, 'remove').name('删除透镜');
            });
        }
    }

    function updateLightControllers() {
        // 移除旧的光源控制器
        if (lightParamsFolder) {
            gui.removeFolder(lightParamsFolder);
            lightParamsFolder = null;
        }

        // 添加新的光源控制器
        if (lightSources.length > 0) {
            lightParamsFolder = gui.addFolder('光源参数');
            lightSources.forEach((lightSource, index) => {
                const lightSubFolder = lightParamsFolder.addFolder(`${lightSource.type === 'point' ? '点' : '平行'}光源 ${index + 1}`);

                lightSubFolder.add(lightSource, 'intensity', 0, 3, 0.1).name('强度').onChange(() => {
                    lightSource.setIntensity(lightSource.intensity);
                });

                lightSubFolder.add(lightSource.position, 'x', -50, 50, 1).name('X位置').onChange(() => {
                    lightSource.updatePosition();
                    calculateLightRays();
                });

                lightSubFolder.add(lightSource.position, 'y', -20, 50, 1).name('Y位置').onChange(() => {
                    lightSource.updatePosition();
                    calculateLightRays();
                });

                lightSubFolder.add(lightSource.position, 'z', -50, 50, 1).name('Z位置').onChange(() => {
                    lightSource.updatePosition();
                    calculateLightRays();
                });

                lightSubFolder.add({ remove: () => {
                    removeLightSource(index);
                    updateLightControllers();
                    calculateLightRays();
                }}, 'remove').name('删除光源');
            });
        }
    }

    // 初始化控制器
    updateLensControllers();
    updateLightControllers();

    return { updateLensControllers, updateLightControllers };
}

// 初始化实验室
function initializeLab() {
    // 初始化默认透镜
    initializeDefaultLenses();

    // 添加一个默认点光源
    const defaultLight = addLightSource('point');
    defaultLight.setPosition(-30, 15, 10);

    // 创建GUI
    const guiControllers = createGUI();
    guiControllers.updateLensControllers();
    guiControllers.updateLightControllers();

    // 计算初始光线
    calculateLightRays();
}

// 窗口自适应
window.addEventListener('resize', onWindowResize, false);

function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 渲染循环
function animate() {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
}

// 启动实验室
initializeLab();
animate();